"""
Azure Exam Analyzer - ShareX + OpenRouter Integration with Failsafe Testing

TESTING FAILSAFE MODES:
To test different failure scenarios, modify the TEST_SIMULATE_* flags in AzureExamAnalyzer.__init__():

1. Test single analyzer failure:
   - Set TEST_SIMULATE_GEMINI_DOWN = True  (tests GPT + Claude workflow)
   - Set TEST_SIMULATE_GPT_DOWN = True     (tests Gemini + Claude workflow)

2. Test Claude failure:
   - Set TEST_SIMULATE_CLAUDE_DOWN = True  (tests analyzer fallback)

3. Test multiple failures:
   - Set TEST_SIMULATE_GEMINI_DOWN = True and TEST_SIMULATE_CLAUDE_DOWN = True
   - Set TEST_SIMULATE_GPT_DOWN = True and TEST_SIMULATE_CLAUDE_DOWN = True

4. Test complete failure:
   - Set all three flags to True (tests error message display)

Alternatively, use the set_test_mode() method programmatically:
   analyzer.set_test_mode(gemini_down=True, gpt_down=False, claude_down=False)
"""

import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
import base64
import requests
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
from openai import OpenAI
from collections import Counter


class AzureExamAnalyzer:
    def __init__(self):
        self.hotkey_detected = False
        self.send_screenshots_detected = False  # New flag for Ctrl+X
        self.running = True
        self.listener = None
        self.root = None
        self.api_key = "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"

        # Define models for the pipeline (removed Mistral)
        self.model_analyzer_1 = "google/gemini-2.5-flash"
        self.model_analyzer_2 = "openai/gpt-5-chat"      # Fictional model name
        self.model_decider = "anthropic/claude-sonnet-4"         # User-specified model

        # Initialize OpenAI client for OpenRouter
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )

        # ShareX output directory
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        self.temp_dir = Path(tempfile.gettempdir()) / "azure_exam_analyzer"
        self.temp_dir.mkdir(exist_ok=True)

        # File monitoring
        self.file_observer = None
        self.latest_file = None
        self.processing_lock = threading.Lock()

        # Multi-screenshot storage
        self.collected_screenshots = []  # Store screenshots in order
        self.screenshot_lock = threading.Lock()  # Protect screenshot collection

        # Testing/Debug configuration - Set these to True to simulate model failures
        self.TEST_SIMULATE_GEMINI_DOWN = False    # Set to True to simulate Gemini failure
        self.TEST_SIMULATE_GPT_DOWN = False       # Set to True to simulate GPT failure
        self.TEST_SIMULATE_CLAUDE_DOWN = False    # Set to True to simulate Claude failure

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()

    def set_test_mode(self, gemini_down=False, gpt_down=False, claude_down=False):
        """Easily set test modes to simulate model failures."""
        self.TEST_SIMULATE_GEMINI_DOWN = gemini_down
        self.TEST_SIMULATE_GPT_DOWN = gpt_down
        self.TEST_SIMULATE_CLAUDE_DOWN = claude_down

        test_modes = []
        if gemini_down: test_modes.append("Gemini DOWN")
        if gpt_down: test_modes.append("GPT DOWN")
        if claude_down: test_modes.append("Claude DOWN")

        if test_modes:
            print(f"🧪 TEST MODE SET: {', '.join(test_modes)}")
        else:
            print("🔧 TEST MODE DISABLED: All models will work normally")
        

    def setup_root(self):
        """Setup the persistent root window."""
        self.root = tk.Tk()
        self.root.withdraw()
        self.root.title("Azure Exam Analyzer")
        
    def setup_file_monitoring(self):
        """Setup file system monitoring for ShareX output."""
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created ShareX directory: {self.sharex_dir}")
        else:
            print(f"✓ Found ShareX directory: {self.sharex_dir}")

        self.file_handler = ShareXFileHandler(self)
        self.file_observer = Observer()
        self.file_observer.schedule(self.file_handler, str(self.sharex_dir), recursive=False)
        self.file_observer.start()

    def show_answer_bubble(self, answer):
        """Show an auto-expanding bubble at top-left that auto-disappears after 20 seconds."""
        def create_bubble():
            bubble = tk.Toplevel(self.root)
            bubble.title("Azure Exam Answer")
            bubble.overrideredirect(True)
            bubble.attributes('-topmost', True)
            bubble.attributes('-alpha', 0.95)

            screen_width = bubble.winfo_screenwidth()
            screen_height = bubble.winfo_screenheight()
            max_width = int(screen_width * 0.4)  # Slightly smaller for top-left positioning
            max_height = int(screen_height * 0.5)

            outer_frame = tk.Frame(bubble, bg='#2E86AB', relief='raised', bd=3)
            outer_frame.pack(padx=10, pady=10)
            inner_frame = tk.Frame(outer_frame, bg='#A23B72', relief='flat', bd=2)
            inner_frame.pack(padx=5, pady=5)

            answer_label = tk.Label(
                inner_frame, text=f"🎯 {answer.strip()}", font=('Arial', 16), fg='white', bg='#A23B72',
                padx=20, pady=20, justify='left', anchor='nw', wraplength=max_width - 60
            )
            answer_label.pack(expand=True, fill='both')

            def close_bubble():
                try: bubble.destroy()
                except: pass

            # Position at top-left, 200 pixels down from top
            bubble.update_idletasks()
            final_width = min(bubble.winfo_reqwidth(), max_width)
            final_height = min(bubble.winfo_reqheight(), max_height)
            x = 20  # 20 pixels from left edge
            y = 200  # 200 pixels from top
            bubble.geometry(f"{final_width}x{final_height}+{x}+{y}")

            # Auto-close after 20 seconds
            bubble.after(20000, close_bubble)  # 20000 ms = 20 seconds

            bubble.bind('<Escape>', lambda e: close_bubble())
            bubble.focus_set()

        if self.root:
            self.root.after(0, create_bubble)


    def on_key_press(self, key):
        """Handle key press events."""
        try:
            if hasattr(key, 'char') and key.char == '\x04': # CTRL+D
                print("Ctrl+D detected!")
                self.hotkey_detected = True
            elif hasattr(key, 'char') and key.char == '\x18': # CTRL+X
                print("Ctrl+X detected!")
                self.send_screenshots_detected = True
        except Exception:
            pass
    
    def check_hotkey(self):
        """Check for hotkey detection."""
        if self.hotkey_detected:
            self.hotkey_detected = False
            print("Launching ShareX for Azure exam question capture...")
            self.launch_sharex_capture()

        if self.send_screenshots_detected:
            self.send_screenshots_detected = False
            print("Ctrl+X detected! Processing all collected screenshots...")
            self.process_collected_screenshots()

        if self.running:
            self.root.after(100, self.check_hotkey)
    
    def start_listener(self):
        """Start the keyboard listener."""
        print("Azure Exam Analyzer - ShareX + OpenRouter Integration")
        print("=" * 55)
        print("Press Ctrl+D to capture Azure exam question screenshots")
        print("Press Ctrl+X to send all collected screenshots to AI for analysis")
        print("Press Ctrl+C to exit")
        print()
        print(f"📁 Monitoring ShareX directory: {self.sharex_dir}")
        print(f"🤖 Analyzer 1: {self.model_analyzer_1}")
        print(f"🤖 Analyzer 2: {self.model_analyzer_2}")
        print(f"⚖️ Decider: {self.model_decider}")

        # Display testing status if any simulations are active
        test_modes = []
        if self.TEST_SIMULATE_GEMINI_DOWN:
            test_modes.append("Gemini DOWN")
        if self.TEST_SIMULATE_GPT_DOWN:
            test_modes.append("GPT DOWN")
        if self.TEST_SIMULATE_CLAUDE_DOWN:
            test_modes.append("Claude DOWN")

        if test_modes:
            print(f"🧪 TEST MODE ACTIVE: {', '.join(test_modes)}")
            print("   (Edit the TEST_SIMULATE_* flags in __init__ to change test scenarios)")
        else:
            print("🔧 To test failsafe modes, edit TEST_SIMULATE_* flags in __init__")
        
        self.listener = keyboard.Listener(on_press=self.on_key_press)
        self.listener.start()
        
        self.check_hotkey()
        
        try:
            self.root.mainloop()
        finally:
            if self.listener: self.listener.stop()
            if self.file_observer:
                self.file_observer.stop()
                self.file_observer.join()
    
    def launch_sharex_capture(self):
        """Launch ShareX region capture using CLI hotkey actions."""
        try:
            subprocess.Popen(['ShareX', '-RectangleRegion'])
            print("✓ ShareX rectangle region capture launched")
        except Exception as e:
            print(f"Error launching ShareX: {e}")

    def encode_image_to_base64(self, image_path):
        """Encode image file to base64 string."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding image: {e}")
            return None
    
    def get_image_mime_type(self, image_path):
        """Get MIME type based on file extension."""
        return {'.png': 'image/png', '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg'}.get(Path(image_path).suffix.lower(), 'image/png')
    
    def create_structured_analysis_prompt(self):
        """Prompt for the initial analyzer models, forcing a structured, step-by-step reasoning process."""
        return """You are a methodical Azure Solutions Architect. Your task is to perform a deep analysis of the provided exam question image. Do not rush to a conclusion. Follow the structured process below precisely.

**Step 1: Deconstruct the Scenario.**
Analyze the image and extract the key information. Fill out the following sections:
- **GOAL:** What is the primary objective to be achieved?
- **SITUATION:** What is the current environment or setup?
- **CONSTRAINTS:** What are the critical requirements or limitations (e.g., cost, performance, availability, security, specific service types like PaaS/IaaS)?

**Step 2: Evaluate Each Option.**
For each option presented in the question, provide a concise analysis of its pros and cons in the context of the deconstructed scenario.
- **Option A Analysis:**
- **Option B Analysis:**
- **Option C Analysis:**
- **Option D Analysis:** (and so on for all options)

**Step 3: Synthesize and Conclude.**
Based on your evaluation, which option is the *best* fit and why? Briefly explain your final reasoning.

**Step 4: State the Final Answer.**
On a new line, write the final answer prefixed with `RECOMMENDED_ANSWER:`. This is a mandatory last step.
"""

    # MODIFIED: Ultra-strict prompt to force Claude to output ONLY the answer with no explanations.
    def create_final_arbiter_prompt(self, analysis1: str, analysis2: str):
        """Ultra-constrained prompt forcing Claude to output only the minimal direct answer."""
        return f"""CRITICAL INSTRUCTION: You must output ONLY the answer. NO explanations, NO reasoning, NO commentary, NO additional text whatsoever.

**FORBIDDEN BEHAVIORS:**
- DO NOT explain your reasoning
- DO NOT provide analysis or commentary
- DO NOT use phrases like "Based on...", "Looking at...", "The answer is..."
- DO NOT use prefixes like "ANSWER:", "SOLUTION:", etc.
- DO NOT provide any text beyond the exact answer

**REQUIRED OUTPUT FORMAT:**
- Single letter for multiple choice (A, B, C, D)
- Exact text/commands for fill-in answers
- Multiple parts on separate lines ONLY if required
- Nothing else

**EXAMPLES OF CORRECT OUTPUT:**
Example 1 (Multiple choice):
C

Example 2 (Two selections):
PolicyDefinitions
DC1

Example 3 (Commands):
Get-ADComputer server2.fabrikam.com
Get-ADComputer server1.contoso.com

**WARNING:** Any explanation, reasoning, or extra text will be considered a failure. Output ONLY the answer.

**CONTEXT FOR REFERENCE:**
Analysis 1: {analysis1}
Analysis 2: {analysis2}

EXAMINE THE SCREENSHOTS AND OUTPUT ONLY THE ANSWER:"""

    def _clean_claude_response(self, raw_response: str) -> str:
        """Clean Claude's response to ensure only the answer is returned."""
        # Remove common unwanted prefixes and explanatory phrases
        unwanted_prefixes = [
            "answer:", "the answer is", "based on", "looking at", "examining",
            "after reviewing", "considering", "solution:", "result:", "final answer:",
            "correct answer:", "the correct answer is", "i can see", "from the screenshots"
        ]

        lines = raw_response.strip().split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if line starts with unwanted prefix
            line_lower = line.lower()
            skip_line = False
            for prefix in unwanted_prefixes:
                if line_lower.startswith(prefix):
                    # Extract text after the prefix
                    remaining = line[len(prefix):].strip()
                    if remaining and not remaining.startswith(':'):
                        line = remaining
                    else:
                        skip_line = True
                    break

            if not skip_line and line:
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines) if cleaned_lines else raw_response.strip()

    def _extract_recommended_answer(self, analysis_text: str) -> str:
        """Extract the RECOMMENDED_ANSWER from analysis text for failsafe scenarios."""
        try:
            for line in reversed(analysis_text.splitlines()):
                if line.upper().startswith("RECOMMENDED_ANSWER:"):
                    return line[len("RECOMMENDED_ANSWER:"):].strip()
        except:
            pass
        # If no RECOMMENDED_ANSWER found, try to extract the last meaningful line
        try:
            lines = [line.strip() for line in analysis_text.splitlines() if line.strip()]
            if lines:
                return lines[-1]
        except:
            pass
        return "Unable to extract answer from analysis"

    def _create_claude_only_prompt(self):
        """Prompt for when Claude must analyze screenshots directly without other analyses."""
        return """CRITICAL INSTRUCTION: You must analyze the Azure exam question in the screenshot(s) and output ONLY the answer. NO explanations, NO reasoning, NO commentary.

**FORBIDDEN BEHAVIORS:**
- DO NOT explain your reasoning
- DO NOT provide analysis or commentary
- DO NOT use phrases like "Based on...", "Looking at...", "The answer is..."
- DO NOT use prefixes like "ANSWER:", "SOLUTION:", etc.
- DO NOT provide any text beyond the exact answer

**REQUIRED OUTPUT FORMAT:**
- Single letter for multiple choice (A, B, C, D)
- Exact text/commands for fill-in answers
- Multiple parts on separate lines ONLY if required
- Nothing else

**EXAMPLES OF CORRECT OUTPUT:**
Example 1 (Multiple choice):
C

Example 2 (Two selections):
PolicyDefinitions
DC1

Example 3 (Commands):
Get-ADComputer server2.fabrikam.com
Get-ADComputer server1.contoso.com

**WARNING:** Any explanation, reasoning, or extra text will be considered a failure. Output ONLY the answer.

EXAMINE THE SCREENSHOTS AND OUTPUT ONLY THE ANSWER:"""

    def analyze_with_openrouter(self, image_paths):
        """Orchestrates the analysis pipeline with comprehensive failsafe mechanisms."""
        try:
            # Prepare all images
            image_content = []
            for i, image_path in enumerate(image_paths):
                base64_image = self.encode_image_to_base64(image_path)
                if not base64_image:
                    print(f"❌ Could not encode image {i+1}: {image_path}")
                    continue
                data_url = f"data:{self.get_image_mime_type(image_path)};base64,{base64_image}"
                image_content.append({"type": "image_url", "image_url": {"url": data_url, "detail": "high"}})

            if not image_content:
                return "Error: Could not encode any images"

            # Create message content with text prompt and all images
            message_content = [{"type": "text", "text": self.create_structured_analysis_prompt()}] + image_content

            # --- STEP 1: Run both analyzer models in parallel with individual error handling ---
            analysis1 = None
            analysis2 = None
            analysis1_success = False
            analysis2_success = False

            def run_analyzer_1():
                """Thread function for Analyzer 1 (Gemini)"""
                nonlocal analysis1, analysis1_success
                try:
                    # TEST: Simulate Gemini failure if testing flag is set
                    if self.TEST_SIMULATE_GEMINI_DOWN:
                        print(f"🧪 TEST MODE: Simulating {self.model_analyzer_1} failure")
                        raise Exception("Simulated failure for testing")

                    print(f"🤔 Starting parallel analysis with {self.model_analyzer_1} on {len(image_paths)} screenshot(s)...")
                    completion1 = self.client.chat.completions.create(
                        model=self.model_analyzer_1,
                        messages=[{"role": "user", "content": message_content}],
                        max_tokens=4096,
                        temperature=0.05,
                    )
                    if completion1.choices and completion1.choices[0].message:
                        analysis1 = completion1.choices[0].message.content.strip()
                        analysis1_success = True
                        print(f"✅ {self.model_analyzer_1} analysis completed successfully")
                    else:
                        print(f"❌ {self.model_analyzer_1} returned empty response")
                        analysis1 = f"Error: {self.model_analyzer_1} returned empty response"
                except Exception as e:
                    print(f"❌ {self.model_analyzer_1} failed: {str(e)}")
                    analysis1 = f"Error: {self.model_analyzer_1} unavailable - {str(e)}"

            def run_analyzer_2():
                """Thread function for Analyzer 2 (GPT)"""
                nonlocal analysis2, analysis2_success
                try:
                    # TEST: Simulate GPT failure if testing flag is set
                    if self.TEST_SIMULATE_GPT_DOWN:
                        print(f"🧪 TEST MODE: Simulating {self.model_analyzer_2} failure")
                        raise Exception("Simulated failure for testing")

                    print(f"🤔 Starting parallel analysis with {self.model_analyzer_2} on {len(image_paths)} screenshot(s)...")
                    completion2 = self.client.chat.completions.create(
                        model=self.model_analyzer_2,
                        messages=[{"role": "user", "content": message_content}],
                        max_tokens=4096,
                        temperature=0.05,
                    )
                    if completion2.choices and completion2.choices[0].message:
                        analysis2 = completion2.choices[0].message.content.strip()
                        analysis2_success = True
                        print(f"✅ {self.model_analyzer_2} analysis completed successfully")
                    else:
                        print(f"❌ {self.model_analyzer_2} returned empty response")
                        analysis2 = f"Error: {self.model_analyzer_2} returned empty response"
                except Exception as e:
                    print(f"❌ {self.model_analyzer_2} failed: {str(e)}")
                    analysis2 = f"Error: {self.model_analyzer_2} unavailable - {str(e)}"

            # Start both analyzers in parallel
            print(f"🚀 Starting parallel analysis with both models...")
            thread1 = threading.Thread(target=run_analyzer_1)
            thread2 = threading.Thread(target=run_analyzer_2)

            thread1.start()
            thread2.start()

            # Wait for both to complete
            thread1.join()
            thread2.join()

            print(f"⏱️ Parallel analysis complete!")

            # --- STEP 2: Determine failsafe strategy based on what succeeded ---
            final_answer = None

            # Check analyzer success status
            analyzers_succeeded = analysis1_success + analysis2_success
            print(f"📊 Analyzer status: {self.model_analyzer_1}={'✅' if analysis1_success else '❌'}, {self.model_analyzer_2}={'✅' if analysis2_success else '❌'}")

            if analyzers_succeeded == 0:
                # FAILSAFE 3: Both analyzers failed, try Claude directly
                print("🚨 FAILSAFE MODE: Both analyzers failed, attempting direct Claude analysis...")
                final_answer = self._try_claude_direct_analysis(image_content)

            else:
                # At least one analyzer succeeded, try Claude with available analyses
                try:
                    if analyzers_succeeded == 2:
                        print(f"⚖️ Submitting both analyses and all screenshots to Claude ({self.model_decider}) for final decision...")
                        prompt_text = self.create_final_arbiter_prompt(analysis1, analysis2)
                    else:
                        # Only one analyzer succeeded
                        successful_analysis = analysis1 if analysis1_success else analysis2
                        successful_model = self.model_analyzer_1 if analysis1_success else self.model_analyzer_2
                        print(f"⚖️ FAILSAFE MODE: Submitting single analysis from {successful_model} to Claude...")
                        prompt_text = self.create_final_arbiter_prompt(successful_analysis, "No second analysis available")

                    arbiter_message_content = [{"type": "text", "text": prompt_text}] + image_content

                    # TEST: Simulate Claude failure if testing flag is set
                    if self.TEST_SIMULATE_CLAUDE_DOWN:
                        print(f"🧪 TEST MODE: Simulating Claude ({self.model_decider}) failure")
                        raise Exception("Simulated Claude failure for testing")

                    decision_completion = self.client.chat.completions.create(
                        model=self.model_decider,
                        messages=[{
                            "role": "user",
                            "content": arbiter_message_content
                        }],
                        max_tokens=250, temperature=0.0,
                    )

                    if decision_completion.choices and decision_completion.choices[0].message:
                        raw_answer = decision_completion.choices[0].message.content.strip()
                        print(f"🔍 Claude raw response: '{raw_answer}'")
                        final_answer = self._clean_claude_response(raw_answer)
                        if final_answer != raw_answer:
                            print(f"🧹 Cleaned response: '{final_answer}'")
                    else:
                        raise ValueError("Claude returned empty response")

                except Exception as e:
                    # FAILSAFE 2: Claude failed, use analyzer fallback
                    print(f"❌ Claude ({self.model_decider}) failed: {str(e)}")
                    final_answer = self._use_analyzer_fallback(analysis1, analysis2, analysis1_success, analysis2_success)

            # --- Handle complete failure ---
            if not final_answer:
                final_answer = "❌ All AI services unavailable. Please check your internet connection and try again later."
                print("🚨 COMPLETE FAILURE: All AI models are unavailable")

            # --- Display the final answer ---
            self.show_answer_bubble(final_answer)
            if final_answer.startswith("❌"):
                print(f"🚨 SYSTEM ERROR: {final_answer}")
            else:
                print(f"🎯 FINAL ANSWER: {final_answer}")
            print("🔄 Ready for next question sequence (press Ctrl+D to start capturing)")
            return final_answer

        except Exception as e:
            error_msg = f"❌ Critical pipeline failure: {str(e)}"
            print(f"🚨 {error_msg}")
            self.show_answer_bubble(error_msg)
            return error_msg

    def _try_claude_direct_analysis(self, image_content):
        """FAILSAFE 3: Try Claude direct analysis when both analyzers fail."""
        try:
            # TEST: Simulate Claude failure if testing flag is set
            if self.TEST_SIMULATE_CLAUDE_DOWN:
                print(f"🧪 TEST MODE: Simulating Claude direct analysis failure")
                raise Exception("Simulated Claude failure for testing")

            print(f"🤔 Attempting direct analysis with Claude ({self.model_decider})...")
            claude_message_content = [{"type": "text", "text": self._create_claude_only_prompt()}] + image_content

            completion = self.client.chat.completions.create(
                model=self.model_decider,
                messages=[{
                    "role": "user",
                    "content": claude_message_content
                }],
                max_tokens=250, temperature=0.0,
            )

            if completion.choices and completion.choices[0].message:
                raw_answer = completion.choices[0].message.content.strip()
                print(f"✅ Claude direct analysis successful")
                print(f"🔍 Claude raw response: '{raw_answer}'")
                final_answer = self._clean_claude_response(raw_answer)
                if final_answer != raw_answer:
                    print(f"🧹 Cleaned response: '{final_answer}'")
                return final_answer
            else:
                print(f"❌ Claude direct analysis returned empty response")
                return None

        except Exception as e:
            print(f"❌ Claude direct analysis failed: {str(e)}")
            return None

    def _use_analyzer_fallback(self, analysis1, analysis2, analysis1_success, analysis2_success):
        """FAILSAFE 2: Use analyzer results when Claude fails."""
        print("🚨 FAILSAFE MODE: Claude unavailable, using analyzer fallback...")

        if analysis1_success and analysis2_success:
            # Both analyzers succeeded, prefer Gemini (analyzer 1)
            print(f"📋 Using {self.model_analyzer_1} analysis as fallback")
            fallback_answer = self._extract_recommended_answer(analysis1)
        elif analysis1_success:
            # Only Gemini succeeded
            print(f"📋 Using {self.model_analyzer_1} analysis as fallback")
            fallback_answer = self._extract_recommended_answer(analysis1)
        elif analysis2_success:
            # Only GPT succeeded
            print(f"📋 Using {self.model_analyzer_2} analysis as fallback")
            fallback_answer = self._extract_recommended_answer(analysis2)
        else:
            # This shouldn't happen in this context, but safety check
            return None

        print(f"🔄 Extracted fallback answer: '{fallback_answer}'")
        return fallback_answer

    def process_new_image(self, image_path):
        """Store newly captured image for later processing."""
        with self.screenshot_lock:
            try:
                print(f"📷 New screenshot detected: {image_path}")
                time.sleep(0.5)
                if not os.path.exists(image_path):
                    print("❌ Image file not found"); return

                # Copy image to temp directory with timestamp to preserve order
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                temp_filename = f"screenshot_{len(self.collected_screenshots)+1:03d}_{timestamp}.png"
                temp_path = self.temp_dir / temp_filename

                shutil.copy2(image_path, temp_path)
                self.collected_screenshots.append(str(temp_path))

                print(f"📸 Screenshot {len(self.collected_screenshots)} stored. Press Ctrl+X to analyze all screenshots.")

                # Clean up original ShareX file
                try:
                    os.remove(image_path)
                    print(f"🗑️ Cleaned up original screenshot: {os.path.basename(image_path)}")
                except Exception as e:
                    print(f"⚠ Could not delete original image: {e}")

            except Exception as e:
                print(f"❌ Error storing image: {e}")
                if os.path.exists(image_path):
                    try: os.remove(image_path)
                    except: pass

    def process_collected_screenshots(self):
        """Process all collected screenshots and send them to AI for analysis."""
        with self.screenshot_lock:
            if not self.collected_screenshots:
                print("❌ No screenshots collected. Take some screenshots first with Ctrl+D.")
                return

            try:
                print(f"🔄 Processing {len(self.collected_screenshots)} screenshot(s)...")

                # Analyze all screenshots together
                self.analyze_with_openrouter(self.collected_screenshots)

                # Clean up all temporary screenshots
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                        print(f"🗑️ Cleaned up temp screenshot: {os.path.basename(screenshot_path)}")
                    except Exception as e:
                        print(f"⚠ Could not delete temp image: {e}")

                # Clear the collection
                self.collected_screenshots.clear()
                print("🔄 Ready for next question sequence (press Ctrl+D to start capturing)")

            except Exception as e:
                print(f"❌ Error processing collected screenshots: {e}")
                # Still clean up on error
                for screenshot_path in self.collected_screenshots:
                    try: os.remove(screenshot_path)
                    except: pass
                self.collected_screenshots.clear()


class ShareXFileHandler(FileSystemEventHandler):
    """File system event handler for ShareX screenshots."""
    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.last_processed = None
    def on_created(self, event):
        if event.is_directory: return
        file_path = event.src_path
        if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            if file_path != self.last_processed:
                self.last_processed = file_path
                threading.Thread(target=self.analyzer.process_new_image, args=(file_path,), daemon=True).start()


def main():
    """Main function."""
    try:
        print("Azure Exam Analyzer - ShareX + OpenRouter Integration")
        print("=" * 55)
        try:
            import watchdog, requests, openai
            print("✓ All dependencies found")
        except ImportError as e:
            print(f"✗ Missing dependency: {e.name}\nPlease run: pip install watchdog requests openai"); return
        try:
            subprocess.run(['ShareX', '--version'], capture_output=True, timeout=3, check=True)
            print("✓ ShareX found and accessible")
        except Exception:
            print("⚠ ShareX may not be in PATH or is not installed.")
        print()
        analyzer = AzureExamAnalyzer()
        analyzer.start_listener()
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()