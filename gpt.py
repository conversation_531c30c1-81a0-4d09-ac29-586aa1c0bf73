import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
import base64
import requests
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
from openai import OpenAI


class AzureExamAnalyzer:
    def __init__(self):
        self.hotkey_detected = False
        self.running = True
        self.listener = None
        self.root = None
        self.api_key = "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"
        self.model = "openai/gpt-5-mini"
        # openai/gpt-4o
        # Initialize OpenAI client for OpenRouter
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )

        # ShareX output directory - only monitor your specific path
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        self.temp_dir = Path(tempfile.gettempdir()) / "azure_exam_analyzer"
        self.temp_dir.mkdir(exist_ok=True)

        # File monitoring
        self.file_observer = None
        self.latest_file = None
        self.processing_lock = threading.Lock()

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()



    def setup_root(self):
        """Setup the persistent root window."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the root window
        self.root.title("Azure Exam Analyzer")

    def setup_file_monitoring(self):
        """Setup file system monitoring for ShareX output."""
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created ShareX directory: {self.sharex_dir}")
        else:
            print(f"✓ Found ShareX directory: {self.sharex_dir}")

        self.file_handler = ShareXFileHandler(self)
        self.file_observer = Observer()

        # Monitor only the specific ShareX directory
        self.file_observer.schedule(self.file_handler, str(self.sharex_dir), recursive=False)
        self.file_observer.start()

    def show_answer_bubble(self, answer):
        """Show an auto-expanding bubble that wraps text and respects screen boundaries, with no scrollbar."""
        def create_bubble():
            bubble = tk.Toplevel(self.root)
            bubble.title("Azure Exam Answer")
            bubble.overrideredirect(True)
            bubble.attributes('-topmost', True)
            bubble.attributes('-alpha', 0.95)

            # Get screen dimensions and define max bubble size (e.g., 50% of screen width)
            screen_width = bubble.winfo_screenwidth()
            screen_height = bubble.winfo_screenheight()
            # MODIFIED: Reduced max width and height for a smaller bubble
            max_width = int(screen_width * 0.5)
            max_height = int(screen_height * 0.5)

            # Main frame with styling
            outer_frame = tk.Frame(bubble, bg='#2E86AB', relief='raised', bd=3)
            outer_frame.pack(padx=10, pady=10)
            inner_frame = tk.Frame(outer_frame, bg='#A23B72', relief='flat', bd=2)
            inner_frame.pack(padx=5, pady=5)

            # Use a Label that can auto-expand. The key is `wraplength`.
            answer_label = tk.Label(
                inner_frame,
                text=f"🎯 {answer.strip()}",
                font=('Arial', 16),
                fg='white',
                bg='#A23B72',
                padx=20,
                pady=20,
                justify='left',  # Align multi-line text to the left
                anchor='nw',     # Anchor text to the top-left
                wraplength=max_width - 60  # IMPORTANT: Wrap text based on max width (minus padding)
            )
            answer_label.pack(expand=True, fill='both')

            def close_bubble():
                try:
                    bubble.destroy()
                except:
                    pass

            close_button = tk.Button(
                inner_frame,
                text="✕ Close",
                font=('Arial', 10, 'bold'),
                fg='white',
                bg='#8B2635',
                activebackground='#A23B72',
                activeforeground='white',
                relief='flat',
                padx=15,
                pady=5,
                command=close_bubble,
                cursor='hand2'
            )
            close_button.pack(pady=(10, 5))

            # Update the window to calculate its required size based on the wrapped content
            bubble.update_idletasks()

            # Get the required size and constrain it to the max dimensions
            req_width = bubble.winfo_reqwidth()
            req_height = bubble.winfo_reqheight()

            final_width = min(req_width, max_width)
            final_height = min(req_height, max_height)

            # MODIFIED: Center horizontally and position at the top of the screen
            x = (screen_width - final_width) // 2
            y = 50  # A fixed distance from the top
            bubble.geometry(f"{final_width}x{final_height}+{x}+{y}")

            # Bind Escape key to close
            bubble.bind('<Escape>', lambda e: close_bubble())
            bubble.focus_set()

        if self.root:
            self.root.after(0, create_bubble)


    def on_key_press(self, key):
        """Handle key press events."""
        try:
            # Detect Ctrl+D
            if hasattr(key, 'char') and key.char == '\x04':
                print("Ctrl+D detected!")
                self.hotkey_detected = True
        except Exception:
            pass

    def check_hotkey(self):
        """Check for hotkey detection."""
        if self.hotkey_detected:
            self.hotkey_detected = False
            print("Launching ShareX for Azure exam question capture...")
            self.launch_sharex_capture()

        if self.running:
            self.root.after(100, self.check_hotkey)

    def start_listener(self):
        """Start the keyboard listener."""
        print("Azure Exam Analyzer - ShareX + OpenRouter Integration")
        print("=" * 55)
        print("Press Ctrl+D to capture Azure exam question")
        print("Press Ctrl+C to exit")
        print()
        print(f"📁 Monitoring ShareX directory: {self.sharex_dir}")
        print(f"🤖 Using model: {self.model}")

        self.listener = keyboard.Listener(on_press=self.on_key_press)
        self.listener.start()

        # Start checking for hotkeys
        self.check_hotkey()

        # Start tkinter main loop
        try:
            self.root.mainloop()
        finally:
            if self.listener:
                self.listener.stop()
            if self.file_observer:
                self.file_observer.stop()
                self.file_observer.join()

    def launch_sharex_capture(self):
        """Launch ShareX region capture using CLI hotkey actions."""
        try:
            cmd = ['ShareX', '-RectangleRegion']
            subprocess.Popen(cmd)
            print("✓ ShareX rectangle region capture launched")

        except FileNotFoundError:
            print("✗ ShareX not found in PATH")
        except Exception as e:
            print(f"Error launching ShareX: {e}")


    def encode_image_to_base64(self, image_path):
        """Encode image file to base64 string."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding image: {e}")
            return None

    def get_image_mime_type(self, image_path):
        """Get MIME type based on file extension."""
        extension = Path(image_path).suffix.lower()
        return {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
        }.get(extension, 'image/png')

    def create_analysis_prompt(self):
        """Prompt for the first AI request: a detailed analysis."""
        return """You are an expert Azure Solutions Architect. Your goal is to meticulously analyze the provided Azure exam question and determine the correct answer.

PROCESS:
1.  **Deconstruct the Question:** Briefly summarize the scenario, goal, and constraints.
2.  **Evaluate the Options:** Analyze each option's viability.
3.  **Synthesize and Conclude:** Provide a concise summary of your reasoning.

After your analysis, you MUST end your response with the final answer on a new line, prefixed with `ANSWER:`. This is a mandatory step. For example:
...and that is why option C is the most cost-effective solution.

ANSWER: C
"""

    def create_extraction_prompt(self, analysis_text: str):
        """Prompt for the second AI request: clean extraction of the final answer."""
        return f"""From the text provided below, extract only the final, definitive answer.
Do not add any explanation, commentary, or any prefixes like "ANSWER:".
Return only the pure answer. If the answer consists of multiple lines, preserve the line breaks.

Here is the text to analyze:
---
{analysis_text}
---

What is the final answer contained in the text above?
"""

    def analyze_with_openrouter(self, image_path):
        """Send image to OpenRouter API for analysis and then extract the clean answer in a second step."""
        try:
            # --- STEP 1: Get Detailed Analysis from AI ---
            print("🔍 Step 1/2: Analyzing exam question...")
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image:
                return "Error: Could not encode image"

            mime_type = self.get_image_mime_type(image_path)
            data_url = f"data:{mime_type};base64,{base64_image}"

            analysis_completion = self.client.chat.completions.create(
                model=self.model,
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": self.create_analysis_prompt()},
                        {"type": "image_url", "image_url": {"url": data_url, "detail": "high"}}
                    ]
                }],
                max_tokens=4096,
                temperature=0.1,
            )

            if analysis_completion.choices and analysis_completion.choices[0].message:
                analysis_text = analysis_completion.choices[0].message.content.strip()
            else:
                raise ValueError("Step 1 (Analysis) returned an empty response from the model.")

            # --- STEP 2: Extract Clean Answer with a Second AI Call ---
            print("✅ Step 2/2: Extracting the final answer...")
            extraction_completion = self.client.chat.completions.create(
                model=self.model, # Can use a faster model here if desired
                messages=[{
                    "role": "user",
                    "content": self.create_extraction_prompt(analysis_text)
                }],
                max_tokens=500, # Answer is expected to be short
                temperature=0.0, # Be very precise
            )

            if extraction_completion.choices and extraction_completion.choices[0].message:
                final_answer = extraction_completion.choices[0].message.content.strip()
            else:
                raise ValueError("Step 2 (Extraction) returned an empty response from the model.")

            # --- Display the final, clean answer ---
            # Replace ' -> ' with a newline for multi-line display
            formatted_answer = final_answer.replace(' -> ', '\n')
            
            self.show_answer_bubble(formatted_answer)
            print(f"🎯 ANSWER:\n{formatted_answer}")
            print("🔄 Ready for next question (press Ctrl+D)")
            return final_answer

        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_answer_bubble(error_msg)
            return f"Error: {error_msg}"

    def process_new_image(self, image_path):
        """Process newly captured image."""
        with self.processing_lock:
            try:
                print(f"📷 New screenshot detected: {image_path}")
                time.sleep(0.5)

                if not os.path.exists(image_path):
                    print("❌ Image file not found")
                    return

                self.analyze_with_openrouter(image_path)

                try:
                    os.remove(image_path)
                    print(f"🗑️ Cleaned up screenshot: {os.path.basename(image_path)}")
                except Exception as e:
                    print(f"⚠ Could not delete image: {e}")

            except Exception as e:
                print(f"❌ Error processing image: {e}")
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                except:
                    pass


class ShareXFileHandler(FileSystemEventHandler):
    """File system event handler for ShareX screenshots."""

    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.last_processed = None

    def on_created(self, event):
        if event.is_directory:
            return

        file_path = event.src_path

        if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            if file_path != self.last_processed:
                self.last_processed = file_path
                threading.Thread(
                    target=self.analyzer.process_new_image,
                    args=(file_path,),
                    daemon=True
                ).start()


def main():
    """Main function."""
    try:
        print("Azure Exam Analyzer - ShareX + OpenRouter Integration")
        print("=" * 55)

        try:
            import watchdog, requests, openai
            print("✓ All dependencies found")
        except ImportError as e:
            print(f"✗ Missing dependency: {e.name}")
            print("Please run: pip install watchdog requests openai")
            return

        try:
            subprocess.run(['ShareX', '--version'], capture_output=True, timeout=3, check=True)
            print("✓ ShareX found and accessible")
        except Exception:
            print("⚠ ShareX may not be in PATH or is not installed.")

        print()

        analyzer = AzureExamAnalyzer()
        analyzer.start_listener()

    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()